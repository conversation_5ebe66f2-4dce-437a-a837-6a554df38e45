import { addCucumberPreprocessorPlugin, afterRun<PERSON>and<PERSON> } from '@badeball/cypress-cucumber-preprocessor';
import { defineConfig } from 'cypress';
import { addEnvToConfig } from './cypress/plugins';
import webpackPreprocessor from '@cypress/webpack-preprocessor';
import { generateCucumberHTMLReport } from './cypress/reporter-config';
import path from 'path';

export default defineConfig({
  viewportWidth: 1900,
  viewportHeight: 1080,
  defaultCommandTimeout: 60000,
  execTimeout: 60000,
  taskTimeout: 60000,
  pageLoadTimeout: 60000,
  requestTimeout: 20000,
  responseTimeout: 60000,
  numTestsKeptInMemory: 20,
  chromeWebSecurity: false,
  projectId: '3aaeqp',
  e2e: {
    setupNodeEvents: async (on, config) => {
      process.env.CYPRESS_TEST = 'true';
      await addCucumberPreprocessorPlugin(on, config, {
        omitAfterRunHandler: true,
      });

      const curConfig = addEnvToConfig(on, config);
      const options = {
        webpackOptions: {
          resolve: {
            extensions: ['.ts', '.tsx', '.js', '.jsx'],
          },
          module: {
            rules: [
              {
                test: /\.feature$/,
                use: [
                  {
                    loader: '@badeball/cypress-cucumber-preprocessor/webpack',
                    options: config,
                  },
                ],
              },
              {
                test: /\.([tj]sx?)$/,
                exclude: [/node_modules/],
                use: [
                  {
                    loader: 'ts-loader',
                    options: {
                      configFile: path.resolve(__dirname, 'cypress/tsconfig.json'),
                      transpileOnly: true,
                      onlyCompileBundledFiles: true,
                    },
                  },
                ],
              },
            ],
          },
        },
        watchOptions: {},
      };
      const data = {};

      on('task', {
        setValue: (params) => {
          const { key, value } = params;
          data[key] = value;
          return value;
        },
        getValue: (params) => {
          const { key } = params;
          return data[key] || null;
        }
      })
      on('file:preprocessor', webpackPreprocessor(options));
      config.env.stepDefinitions = 'cypress/e2e/step_definitions/[filepath]/*.{js,ts}';
      on('after:run', async (results) => {
        await afterRunHandler(config);
        await generateCucumberHTMLReport(results, config);
      });
      return curConfig;
    },
    baseUrl: 'https://local.veritone.com:3001',
    numTestsKeptInMemory: 8,
    video: false,
    retries: {
      runMode: 1,
      openMode: 0
    },
    specPattern: 'cypress/e2e/**/*.feature',
  },
});
