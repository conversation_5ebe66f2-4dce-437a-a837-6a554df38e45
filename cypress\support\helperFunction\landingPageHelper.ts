import { cloneDeep } from 'lodash';
import moment from 'moment';

export const enum DataTestSelector {
  TopBar = '@mainpage-tdo-topbarbtt',
  Grid = '@tdos-grid-container',
  Checkbox = '@mainpage-tdo-checkbox',
  FileName = '@tdo-wrapper-file-name',
  DateTime = '@mainpage-datetime',
  Status = '@mainpage-status',
  DelBtn = '@delete-tdos-button',
  ConfirmDelBtn = '@confirm-delete-tdos-ok-button',
}
export const enum Graphql {
  ListSDOQuery = '\n      query listSDOs($schemaId: ID!, $limit: Int!, $offset: Int, $filter: JSONData) {\n        structuredDataObjects(\n          schemaId: $schemaId,\n          limit: $limit,\n          offset: $offset,\n          orderBy: [{field: modifiedDateTime, direction: desc}],\n          owned: true,\n          filter: $filter,\n        ) {\n          records {\n            id\n            data\n            createdDateTime\n            modifiedDateTime\n          }\n        }\n      }\n    ',
}

export interface Tag {
  readonly value: string;
  readonly redactionStatus: string;
}

export function deleteTdoByName(nameList: string[]) {
  cy.Graphql<FetchOrganizationSDOsResponse>(Graphql.ListSDOQuery, {
    schemaId: 'dbbca101-b758-4225-bbd0-54303f20ae7d',
    limit: 10,
    offset: 0,
    filter: null,
  }).then((res) => {
    const records = res.body.data.structuredDataObjects.records;

    const tdoIdList = records
      .map((record) => record.data.tdoId)
      .filter((id) => id);
    const query = `query mapTDOs { ${tdoIdList.map((tdoId) => `id${String(tdoId).replace(/-/g, '')}: temporalDataObject(id: ${JSON.stringify(tdoId)}) { id name status thumbnailUrl createdDateTime modifiedDateTime startDateTime stopDateTime details tasks (limit: 300) { records { id status createdDateTime startedDateTime completedDateTime engineId engine { id categoryId name } jobId job { status } } } assets(assetType: ["redacted-media", "redacted-media-clip"], limit: 1) { count } }`).join(',')} }`;
    return cy.Graphql(query).then((res) => {
      const tdoObjects = res.body;
      const filteredRecords = Object.values(
        tdoObjects.data as GraphQLTDOData
      ).filter(
        (tdo: TDOObject | null): tdo is TDOObject =>
          tdo !== null && Boolean(tdo.id)
      );
      const tdosToProcessByName = filteredRecords.filter(
        (record: TDOObject) =>
          record &&
          record.name &&
          nameList.some((nameInList) => record.name === nameInList)
      );
      const itemsToSoftDelete: { tdo: TDOObject; sdoUUID: string }[] = [];
      tdosToProcessByName.forEach((tdoToProcess: TDOObject) => {
        const correspondingSdo = records.find(
          (sdoRecord) => sdoRecord.data.tdoId === tdoToProcess.id
        );
        if (correspondingSdo && correspondingSdo.id) {
          itemsToSoftDelete.push({
            tdo: tdoToProcess,
            sdoUUID: correspondingSdo.id,
          });
        }
      });
      if (itemsToSoftDelete.length === 0) {
        return;
      }
      console.log(`Total count of matching records: ${filteredRecords.length}`);
      const removeKeyQuotes = (value: string) =>
        value?.replace(/"([^"]+)":/g, '$1:');
      const buildTdoDetails = (tdo: TDOObject) => {
        const originalTdoDetailsObject = tdo.details || {};
        const gqlSafeBaseDetails: Record<string, unknown> = {};
        for (const key in originalTdoDetailsObject) {
          if (
            Object.prototype.hasOwnProperty.call(originalTdoDetailsObject, key)
          ) {
            if (
              key !== 'tags' &&
              /^[_A-Za-z]/.test(key) &&
              key.match(/^[_A-Za-z][_0-9A-Za-z]*$/)
            ) {
              gqlSafeBaseDetails[key] = originalTdoDetailsObject[key];
            }
          }
        }

        const originalTags = originalTdoDetailsObject.tags;
        const updatedSystemTags = cloneDeep(
          Array.isArray(originalTags) ? originalTags : []
        );
        if (updatedSystemTags.length > 0 && updatedSystemTags[0]) {
          updatedSystemTags[0].toBeDeletedTime = moment(
            new Date()
          ).toISOString();
        }
        const mutationInputDetails = {
          ...gqlSafeBaseDetails,
          name: tdo.name,
          addToIndex: true,
          tags: ['in redaction'],
        };
        return removeKeyQuotes(JSON.stringify(mutationInputDetails));
      };
      cy.log(JSON.stringify(filteredRecords[0]));
      const adquery = `mutation softDeleteTDOs {
        ${itemsToSoftDelete
          .map((item, index) => {
            const tdoIdString = JSON.stringify(item.tdo.id);
            const detailsString = buildTdoDetails(item.tdo);
            return `i${index}:updateTDO(input: {id:${tdoIdString}, details: ${detailsString}}) { id }`;
          })
          .join('\n        ')}
      }`;
      cy.log(adquery);
      return cy.Graphql(adquery).then(() => {
        const updateSdoMutationQuery = `mutation softDeleteSDOs {
          ${itemsToSoftDelete
            .map((item, index) => {
              const sdoDataPayloadString = `{tdoId:${JSON.stringify(item.tdo.id)}, status:${JSON.stringify('Draft')}, toBeDeletedTime:${JSON.stringify(moment().toISOString())}}`;
              return `i${index}:createStructuredData(
                input: {
                  id: ${JSON.stringify(item.sdoUUID)},
                  schemaId: ${JSON.stringify('dbbca101-b758-4225-bbd0-54303f20ae7d')},
                  data: ${sdoDataPayloadString} 
                }) {
                id
              }`;
            })
            .join('\n          ')}
        }`;
        cy.log(updateSdoMutationQuery);
        return cy.Graphql(updateSdoMutationQuery);
      });
    });
  });
}
