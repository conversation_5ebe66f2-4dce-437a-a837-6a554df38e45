{"compilerOptions": {"target": "es2023", "module": "ESNext", "lib": ["es2023", "dom"], "skipLibCheck": true, "allowJs": true, "jsx": "react-jsx", "declaration": false, "sourceMap": true, "removeComments": true, "importHelpers": true, "downlevelIteration": true, "resolveJsonModule": true, "strict": false, "noImplicitAny": false, "strictNullChecks": false, "strictFunctionTypes": false, "strictPropertyInitialization": false, "noImplicitThis": false, "noUnusedLocals": false, "noUnusedParameters": false, "moduleResolution": "node", "baseUrl": "./", "typeRoots": ["../node_modules/@types"], "types": ["cypress", "node"], "allowSyntheticDefaultImports": true, "esModuleInterop": true, "preserveConstEnums": true, "experimentalDecorators": true, "emitDecoratorMetadata": true}, "include": ["**/*.js", "**/*.ts", "**/*.tsx"], "exclude": ["node_modules"]}